VITE_API_BASE_URL=http://localhost:3333/api
VITE_WEB_BASE_URL=http://localhost:4200
VITE_WEB_PORT=4200

NODE_ENV=development
PORT=3333
JWT_SECRET_KEY=your-secret-key
MONGO_URI=mongodb://localhost/cvinventory
DISABLE_REQUEST_LOGS=false
VITE_DEPLOYMENT_TYPE=test

# Email configuration - Safe by default
# Set to 'true' to send real emails (for production only)
# Default: false (emails are mocked/logged for safety)
SEND_REAL_EMAILS=false
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=your-mail-from-address
SENDGRID_REPLY_TO=your-mail-to-address

AWS_REGION=eu-north-1
AWS_BUCKET=
AWS_BUCKET_URL=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_INSTANCE_ID=

STRIPE_SECRET_KEY=sk_test_YOUR_STRIPE_SECRET_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_STRIPE_WEBHOOK_SECRET
STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_STRIPE_PUBLISHABLE_KEY

CV_INVENTORY_TOKEN=token
AI_API_URL=https://your-ai-api-url.com
AI_API_TOKEN=your-ai-api-token
