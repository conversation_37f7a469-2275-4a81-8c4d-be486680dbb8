import { ForbiddenError } from '@casl/ability';
import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { genSalt, hash, compare } from 'bcrypt';
import { escapeRegExp } from 'lodash';
import { Model, Types } from 'mongoose';
import { UpdateUserDto } from 'shared/dto/user/update-user.dto';
import { UserRole } from 'shared/types';

// Role validation error messages
const USER_ROLE_ERRORS = {
  MEMBER_CANNOT_CHANGE_ROLES: 'Members cannot change user roles',
  CANNOT_CHANGE_OWN_ROLE: 'Users cannot change their own role',
  ADMIN_CANNOT_ASSIGN_OWNER: 'Admins cannot assign Owner role',
  ONLY_OWNER_CAN_ASSIGN_OWNER: 'Only Owners can assign Owner role',
} as const;

import { UserStatus } from './user.schema';
import { User, UserDocument } from './user.schema';
import { CaslAbilityFactory } from '../casl/casl-ability.factory';
import { Action } from '../casl/casl.types';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { S3Provider } from '../global/s3.provider';
import { MailService } from '../mail/mail.service';
import { Organization } from '../organization/organization.schema';
import { OrganizationService } from '../organization/organization.service';

const DELETION_DELAY_DAYS = 7;

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private readonly s3Provider: S3Provider,
    private readonly caslAbilityFactory: CaslAbilityFactory,
    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,
    private readonly mailService: MailService,
  ) {}

  async findAuthUser(query: Partial<User>): Promise<User | null> {
    return this.userModel.findOne(query).exec();
  }

  async findUserWithPassword(query: Partial<User>): Promise<User | null> {
    return this.userModel.findOne(query).select('+password').exec();
  }
  async findById(id: string): Promise<User | null> {
    return this.userModel.findById(id).exec();
  }
  async findByEmail(email: string): Promise<User | null> {
    return this.userModel.findOne({ email }).exec();
  }

  async findOneAndPopulateOrganization(
    query: Partial<User>,
    projection: string,
  ): Promise<User | null> {
    return this.userModel.findOne(query).select(projection).lean().exec();
  }

  async findOne(query: Partial<User>): Promise<User | null> {
    return this.userModel.findOne(query).exec();
  }

  async setPassword(email: string, newPassword: string) {
    const user = await this.findOne({ email: email });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const salt = await genSalt();
    const password = await hash(newPassword, salt);

    return await this.userModel
      .findOneAndUpdate(
        { _id: user._id },
        { $set: { password, status: UserStatus.active } },
      )
      .exec();
  }

  async create(user: Partial<User>): Promise<User> {
    const userDoc = await this.userModel.create(user);
    return userDoc.toObject();
  }

  existByEmail(email: string) {
    return this.userModel.exists({ email });
  }

  async updatePassword(
    userId: string | Types.ObjectId,
    hashedPassword: string,
  ) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.userModel
      .findByIdAndUpdate(
        userId,
        { $set: { password: hashedPassword } },
        { new: true },
      )
      .exec();
  }

  async getOrganizationUsers(
    organizationId: string | Types.ObjectId,
    searchTerm?: string,
    includeScheduledForDeletion = false,
  ): Promise<User[]> {
    const aggregation = [];

    // Match users belonging to the specified organization
    // TODO: you can use @casl/mongoose here, research that.
    const matchConditions: any = {
      'availableOrganizations.orgId': new Types.ObjectId(organizationId),
    };

    if (!includeScheduledForDeletion) {
      matchConditions.deletionScheduledDate = { $exists: false };
    }

    aggregation.push({
      $match: matchConditions,
    });

    // If a search term is provided, add a search stage
    if (searchTerm) {
      const escapedSearchTerm = escapeRegExp(searchTerm);
      aggregation.push({
        $match: {
          $or: [
            { firstName: { $regex: escapedSearchTerm, $options: 'i' } },
            { lastName: { $regex: escapedSearchTerm, $options: 'i' } },
            { email: { $regex: escapedSearchTerm, $options: 'i' } },
          ],
        },
      });
    }

    // Add a projection stage to reshape the documents
    aggregation.push({
      $addFields: {
        organizationMembership: {
          $filter: {
            input: '$availableOrganizations',
            as: 'org',
            cond: { $eq: ['$$org.orgId', new Types.ObjectId(organizationId)] },
          },
        },
      },
    });

    aggregation.push({
      $unwind: '$organizationMembership',
    });

    // Final projection to format the output
    aggregation.push({
      $project: {
        _id: 1,
        firstName: 1,
        lastName: 1,
        email: 1,
        avatar: 1,
        role: '$organizationMembership.role',
        joinedDate: '$organizationMembership.joinedDate',
      },
    });

    return this.userModel.aggregate(aggregation).exec();
  }

  async setActiveOrganization(
    userId: string | Types.ObjectId,
    organizationId: string | Types.ObjectId,
  ) {
    return this.userModel
      .findByIdAndUpdate(
        userId,
        { $set: { organization: organizationId } },
        { new: true },
      )
      .exec();
  }

  async getUserOrganizations(
    userId: string | Types.ObjectId,
    includeScheduledForDeletion = false,
  ) {
    const user = await this.userModel
      .findById(userId)
      .select('availableOrganizations')
      .populate<{
        availableOrganizations: {
          orgId: Organization;
          role: UserRole;
          joinedDate: Date;
        }[];
      }>('availableOrganizations.orgId')
      .lean()
      .exec();

    const organizations = user?.availableOrganizations || [];

    const filteredOrganizations = includeScheduledForDeletion
      ? organizations
      : organizations.filter(
          (orgMembership) => !orgMembership.orgId.deletionScheduledDate,
        );

    return filteredOrganizations.map((orgMembership) => ({
      _id: orgMembership.orgId._id,
      name: orgMembership.orgId.name,
      photo: orgMembership.orgId.photo,
      role: orgMembership.role,
      joinedDate: orgMembership.joinedDate,
    }));
  }

  async addUserToOrganization(
    organizationId: string | Types.ObjectId,
    userId: string | Types.ObjectId,
    role: UserRole,
  ) {
    return this.userModel.findByIdAndUpdate(
      userId,
      {
        $push: {
          availableOrganizations: {
            orgId: organizationId,
            role,
            joinedDate: new Date(),
          },
        },
        $set: {
          organization: organizationId,
        },
      },
      { new: true },
    );
  }

  async uploadUserAvatar(userId: string, avatarFile: Express.Multer.File) {
    if (!avatarFile) {
      throw new Error('File is not passed');
    }

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    // If user already has an avatar, delete it from S3
    if (user.avatar) {
      try {
        const oldAvatarKey = user.avatar.replace(
          new RegExp('^' + process.env.AWS_BUCKET_URL + '/'),
          '',
        );
        await this.s3Provider.delete(oldAvatarKey);
      } catch (error) {
        console.error(`Failed to delete old avatar for user ${userId}:`, error);
      }
    }

    const s3Key = `users/${userId}/avatars/${new Date().getTime()}-${avatarFile.originalname.replace(/\s+/g, '_')}`;

    const sendData = await this.s3Provider.save(
      avatarFile.buffer,
      avatarFile.mimetype,
      s3Key,
    );

    return this.userModel
      .findByIdAndUpdate(
        userId,
        { $set: { avatar: sendData.Location } },
        { new: true },
      )
      .exec();
  }

  async removeUserAvatar(userId: string): Promise<User | null> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    if (user.avatar) {
      try {
        const avatarKey = user.avatar.replace(
          new RegExp('^' + process.env.AWS_BUCKET_URL + '/'),
          '',
        );
        await this.s3Provider.delete(avatarKey);
      } catch (error) {
        console.error(
          `Failed to delete avatar from S3 for user ${userId}:`,
          error,
        );
        // Decide if this should be a critical error or just logged
      }
    }

    return this.userModel
      .findByIdAndUpdate(userId, { $unset: { avatar: '' } }, { new: true })
      .exec();
  }

  async updateUser(
    userId: string,
    updateUserDto: UpdateUserDto,
    authUser: AuthUserDto,
  ): Promise<User | null> {
    // 1. Fetch the target user
    const targetUser = await this.findById(userId);
    if (!targetUser) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    // 2. Validate role update before CASL check
    if (updateUserDto.role !== undefined) {
      this.validateRoleUpdate(authUser, targetUser, updateUserDto.role);
    }

    // 3. Check permissions using CASL
    const ability = this.caslAbilityFactory.createForUser(authUser);
    const userForCheck = {
      ...targetUser,
      _id: targetUser._id.toString(),
      constructor: User,
      // Convert availableOrganizations orgId to string for CASL comparison
      availableOrganizations:
        targetUser.availableOrganizations?.map((org) => ({
          ...org,
          orgId: org.orgId.toString(),
        })) || [],
    } as unknown as User & { _id: string };

    try {
      ForbiddenError.from(ability).throwUnlessCan(Action.Update, userForCheck);
    } catch (error) {
      if (error instanceof ForbiddenError) {
        throw new ForbiddenException(error.message);
      }
      throw error;
    }

    // 3. Handle avatar removal if explicitly set to null in DTO and not handled by a file upload
    if (updateUserDto.avatar === null && targetUser.avatar) {
      await this.removeUserAvatar(userId);
    }

    // 4. Prepare DTO for update, excluding avatar and role if they were handled separately
    const dtoToUpdate: Partial<UpdateUserDto> = { ...updateUserDto };
    if (updateUserDto.avatar === null) {
      delete dtoToUpdate.avatar; // Avatar removal is handled by $unset via removeUserAvatar
    }

    // Handle role update separately since it's stored in organizationMembership
    if (updateUserDto.role !== undefined) {
      delete dtoToUpdate.role; // Remove role from the general update

      // Update the role in the specific organization membership
      await this.userModel
        .findOneAndUpdate(
          {
            _id: userId,
            'availableOrganizations.orgId': authUser.organization._id,
          },
          {
            $set: {
              'availableOrganizations.$.role': updateUserDto.role,
            },
          },
        )
        .exec();
    }

    // 5. Perform the update for other fields
    const updatedUser = await this.userModel
      .findByIdAndUpdate(userId, { $set: dtoToUpdate }, { new: true })
      .exec();

    if (!updatedUser) {
      // This case should ideally not be reached if findById above succeeded
      throw new NotFoundException(
        `User with ID "${userId}" not found during update`,
      );
    }
    return updatedUser;
  }

  private validateRoleUpdate(
    authUser: AuthUserDto,
    targetUser: User,
    newRole: UserRole,
  ): void {
    const isEditingSelf = authUser._id.toString() === targetUser._id.toString();
    const currentUserRole = authUser.role;

    // Rule 1: Members cannot change any roles
    if (currentUserRole === UserRole.MEMBER) {
      throw new ForbiddenException(USER_ROLE_ERRORS.MEMBER_CANNOT_CHANGE_ROLES);
    }

    // Rule 2: Users cannot change their own role (prevents self-promotion)
    if (isEditingSelf) {
      throw new ForbiddenException(USER_ROLE_ERRORS.CANNOT_CHANGE_OWN_ROLE);
    }

    // Rule 3: Admins cannot assign OWNER role
    if (currentUserRole === UserRole.ADMIN && newRole === UserRole.OWNER) {
      throw new ForbiddenException(USER_ROLE_ERRORS.ADMIN_CANNOT_ASSIGN_OWNER);
    }

    // Rule 4: Only owners can assign OWNER role
    if (newRole === UserRole.OWNER && currentUserRole !== UserRole.OWNER) {
      throw new ForbiddenException(
        USER_ROLE_ERRORS.ONLY_OWNER_CAN_ASSIGN_OWNER,
      );
    }
  }

  async deleteOrganizationUsers(
    organizationId: string | Types.ObjectId,
  ): Promise<{ modifiedCount?: number }> {
    const result = await this.userModel.updateMany(
      { 'availableOrganizations.orgId': organizationId },
      { $pull: { availableOrganizations: { orgId: organizationId } } },
    );
    return { modifiedCount: result.modifiedCount };
  }

  /**
   * Mark a user for deletion at a specific date
   * Used when a user's only organization is scheduled for deletion
   */
  async markUserForDeletion(
    userId: string | Types.ObjectId,
    deletionDate: Date | null,
  ): Promise<User | null> {
    return this.userModel.findByIdAndUpdate(
      userId,
      { deletionScheduledDate: deletionDate },
      { new: true },
    );
  }

  /**
   * Execute deletion of users that have reached their scheduled deletion date
   * Called by the organization service's executeScheduledDeletions method
   */
  async executeScheduledUserDeletions(): Promise<{ deletedCount: number }> {
    const today = new Date();

    // Find users scheduled for deletion today or earlier
    const usersToDelete = await this.userModel.find({
      deletionScheduledDate: { $lte: today },
    });

    this.logger.log(
      `Found ${usersToDelete.length} users scheduled for deletion (manual account deletions).`,
    );

    // Delete each user (these are manual account deletions, org deletions are handled elsewhere)
    for (const user of usersToDelete) {
      try {
        // Check if user still has organizations - if not, they were deleted due to org deletion
        const userOrgs = await this.getUserOrganizations(user._id);

        if (userOrgs.length > 0) {
          // User still has organizations, this is a manual account deletion
          await this.mailService.sendUserAccountDeletedConfirmation(
            user.email,
            user.firstName,
            user.lastName,
          );
          this.logger.log(
            `Sent manual account deletion email to ${user.email}`,
          );
        } else {
          // User has no organizations left, they already received org deletion email
          this.logger.log(
            `User ${user._id} has no organizations left - skipping manual deletion email (already received org deletion email)`,
          );
        }

        await this.userModel.findByIdAndDelete(user._id);
        this.logger.log(`Deleted user ${user._id} as scheduled.`);
      } catch (error) {
        this.logger.error(`Error deleting user ${user._id}:`, error);
      }
    }

    return { deletedCount: usersToDelete.length };
  }

  async cancelUserDeletion(userId: string): Promise<User | null> {
    const user = await this.userModel
      .findById(userId)
      .select('+deletionScheduledDate');

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.deletionScheduledDate) {
      throw new BadRequestException('No deletion is scheduled for this user');
    }

    this.logger.log(`Canceling deletion for user ${userId}.`);

    // Remove the deletion scheduled date
    const updatedUser = await this.userModel.findByIdAndUpdate(
      userId,
      { $unset: { deletionScheduledDate: '' } },
      { new: true },
    );

    this.logger.log(`Deletion canceled for user ${userId}.`);

    return updatedUser;
  }

  async deleteUser(userId: string, password: string) {
    const user = await this.userModel.findById(userId).select('+password');

    if (!user) {
      this.logger.warn(`User ${userId} not found for deletion.`);
      throw new NotFoundException('User not found');
    }

    // Verify password
    const isPasswordValid = await compare(password, user.password);
    if (!isPasswordValid) {
      throw new BadRequestException('Invalid password');
    }

    this.logger.log(`Starting deletion process for user ${userId}.`);

    // Check if user is the sole owner of any organization
    const userOrganizations = await this.getUserOrganizations(userId);
    for (const org of userOrganizations) {
      const userMembership = user.availableOrganizations?.find(
        (membership) => membership.orgId.toString() === org._id.toString(),
      );

      if (userMembership?.role === UserRole.OWNER) {
        // Check if there are other owners in this organization
        const orgUsers = await this.getOrganizationUsers(
          org._id,
          undefined,
          true,
        );
        this.logger.log(
          `Organization ${org._id} has ${orgUsers.length} total users.`,
        );

        const otherOwners = orgUsers.filter((orgUser: any) => {
          // The getOrganizationUsers method projects the role directly
          const isOwner = orgUser.role === UserRole.OWNER;
          const isDifferentUser = orgUser._id.toString() !== userId;

          this.logger.log(
            `User ${orgUser._id}: role=${orgUser.role}, isOwner=${isOwner}, isDifferentUser=${isDifferentUser}`,
          );

          return isOwner && isDifferentUser;
        });

        this.logger.log(
          `Found ${otherOwners.length} other owners in organization ${org._id}.`,
        );

        if (otherOwners.length === 0) {
          this.logger.warn(
            `User ${userId} is the sole owner of organization ${org._id}. Cannot delete.`,
          );
          throw new BadRequestException(
            "You're the only owner in one of your organizations. To delete your account, please assign ownership to another member first.",
          );
        }
      }
    }

    const deletionDate = new Date();
    deletionDate.setDate(deletionDate.getDate() + DELETION_DELAY_DAYS);

    // Mark user for deletion
    await this.userModel.findByIdAndUpdate(userId, {
      deletionScheduledDate: deletionDate,
    });

    this.logger.log(
      `User ${userId} scheduled for deletion on ${deletionDate.toISOString()}.`,
    );

    // Send notification email to user about the scheduled deletion
    await this.mailService.sendUserAccountDeletionNotification(
      user.email,
      user.firstName,
      user.lastName,
      deletionDate,
    );

    // Get user's organizations
    this.logger.log(
      `User ${userId} is member of ${userOrganizations.length} organization(s).`,
    );

    // Check each organization to see if user is the only member
    for (const org of userOrganizations) {
      this.logger.log(
        `Checking organization ${org._id} for sole membership by user ${userId}.`,
      );

      const orgUsers = await this.getOrganizationUsers(
        org._id,
        undefined,
        true,
      );

      if (orgUsers.length === 1 && orgUsers[0]._id.toString() === userId) {
        this.logger.log(
          `User ${userId} is the only member of organization ${org._id}. Scheduling organization for deletion.`,
        );

        // Schedule organization for deletion
        await this.organizationService.deleteOrganization(
          org._id,
          userId,
          'system-deletion',
        );

        this.logger.log(
          `Organization ${org._id} has been scheduled for deletion due to user ${userId} being the sole member.`,
        );
      } else {
        this.logger.log(
          `Organization ${org._id} has ${orgUsers.length} members. No action needed.`,
        );
      }
    }

    this.logger.log(`Deletion process completed for user ${userId}.`);

    return { message: 'User scheduled for deletion' };
  }
}
