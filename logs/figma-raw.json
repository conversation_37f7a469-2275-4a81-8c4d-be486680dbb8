{"name": "🌰 MuchSkills MASTER", "lastModified": "2025-08-14T10:10:54Z", "thumbnailUrl": "https://s3-alpha.figma.com/thumbnails/48fa03f1-8be2-4c11-a9b8-f0d40490340d?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCWOTIVFC2%2F20250814%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250814T000000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=8e07e5f14636c6cc60ba64c6bda4a740bf2a3eecec06de5be9107262b0ccde33", "version": "2252078647831994566", "role": "viewer", "editorType": "figma", "linkAccess": "inherit", "nodes": {"24177:59160": {"document": {"id": "24177:59160", "name": "Frame 1065", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59161", "name": "Frame 117429", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59162", "name": "dropdown", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "620:4151", "componentProperties": {"type": {"value": "text_ic", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "24177:59162", "overriddenFields": ["height", "inheritFillStyleId", "width"]}, {"id": "I24177:59162;253:328", "overriddenFields": ["characterStyleOverrides", "characters", "inheritTextStyleId", "lineIndentations", "lineTypes", "styleOverrideTable", "text"]}], "children": [{"id": "I24177:59162;253:328", "name": "3 months", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:104", "text": "1:109"}, "absoluteBoundingBox": {"x": -1019, "y": -5721, "width": 63, "height": 16}, "absoluteRenderBounds": {"x": -1018.5482177734375, "y": -5716.916015625, "width": 61.62786865234375, "height": 11.08203125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Showing All", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Bold", "fontStyle": "Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 12, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "I24177:59162;253:347", "name": "ic-dropdown-small", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:180", "overrides": [], "children": [{"id": "I24177:59162;253:347;250:18", "name": "Vector 36 (<PERSON><PERSON>)", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062848, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeJoin": "ROUND", "strokeCap": "ROUND", "styles": {"fill": "1:104"}, "absoluteBoundingBox": {"x": -951.9999996980084, "y": -5715.000000349691, "width": 7.999999698008423, "height": 4.0000005881092875}, "absoluteRenderBounds": {"x": -952, "y": -5715, "width": 8, "height": 4}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -956, "y": -5721, "width": 16.000000953674203, "height": 16.000000953674316}, "absoluteRenderBounds": {"x": -956, "y": -5721, "width": 16.000000953674203, "height": 16.000000953674316}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8784313797950745, "g": 0.8784313797950745, "b": 0.8784313797950745, "a": 1}}], "cornerRadius": 100, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 1, "g": 1, "b": 1, "a": 1}, "styles": {"fills": "1:103", "strokes": "1:106", "fill": "1:103", "stroke": "1:106"}, "layoutMode": "HORIZONTAL", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 8, "paddingRight": 8, "paddingTop": 2, "paddingBottom": 2, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -1027, "y": -5723, "width": 95, "height": 20}, "absoluteRenderBounds": {"x": -1027, "y": -5723, "width": 95, "height": 20}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "24177:59163", "name": "Frame 117417", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59164", "name": "Frame 117403", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59165", "name": "Rows per page", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "1:108"}, "absoluteBoundingBox": {"x": -182, "y": -5721, "width": 77, "height": 16}, "absoluteRenderBounds": {"x": -180.85707092285156, "y": -5716.59814453125, "width": 75.34630584716797, "height": 10.7880859375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Rows per page", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 12, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59166", "name": "Frame 117402", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59167", "name": "10", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "1:109"}, "absoluteBoundingBox": {"x": -97, "y": -5721, "width": 14, "height": 16}, "absoluteRenderBounds": {"x": -96.17493438720703, "y": -5716.77197265625, "width": 12.834938049316406, "height": 8.8681640625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "10", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Bold", "fontStyle": "Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 12, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59168", "name": "ic-dropdown-small", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:180", "overrides": [{"id": "24177:59168", "overriddenFields": ["height", "width"]}, {"id": "I24177:59168;250:18", "overriddenFields": ["inheritFillStyleId"]}], "children": [{"id": "I24177:59168;250:18", "name": "Vector 36 (<PERSON><PERSON>)", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062848, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeJoin": "ROUND", "strokeCap": "ROUND", "styles": {"fill": "1:182"}, "absoluteBoundingBox": {"x": -78.9999996980084, "y": -5715.000000349691, "width": 7.999999698008395, "height": 4.0000005881092875}, "absoluteRenderBounds": {"x": -79, "y": -5715, "width": 8, "height": 4}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -83, "y": -5721, "width": 16.000000953674203, "height": 16.000000953674316}, "absoluteRenderBounds": {"x": -83, "y": -5721, "width": 16.000000953674203, "height": 16.000000953674316}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -97, "y": -5721, "width": 30, "height": 16}, "absoluteRenderBounds": {"x": -97, "y": -5721, "width": 30, "height": 16}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "itemSpacing": 8, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -182, "y": -5721, "width": 115, "height": 16}, "absoluteRenderBounds": {"x": -182, "y": -5721, "width": 115, "height": 16}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "24177:59169", "name": "pages", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "908:21480", "componentProperties": {"type": {"value": "start", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "24177:59169", "overriddenFields": ["height", "layoutPositioning", "width"]}], "children": [{"id": "I24177:59169;908:21452", "name": "ic-right", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "rotation": 2.0989742381843717e-18, "componentId": "1:110", "overrides": [], "uniformScaleFactor": 1.5, "children": [{"id": "I24177:59169;908:21452;764:5", "name": "Vector 36 (<PERSON><PERSON>)", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": -3.1415925661670165, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8784313797950745, "g": 0.8784313797950745, "b": 0.8784313797950745, "a": 1}}], "strokes": [], "strokeWeight": 1.5, "strokeAlign": "CENTER", "strokeJoin": "ROUND", "strokeCap": "ROUND", "styles": {"fill": "1:106"}, "absoluteBoundingBox": {"x": -58.00000059604639, "y": -5718.999999809281, "width": 6.0000016451196245, "height": 11.99999980928078}, "absoluteRenderBounds": {"x": -58, "y": -5719, "width": 6, "height": 12}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -67, "y": -5725, "width": 24.000002384185564, "height": 24.00000238418579}, "absoluteRenderBounds": {"x": -67, "y": -5725, "width": 24.000003814697266, "height": 24.00000238418579}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I24177:59169;908:21453", "name": "Title", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "1:109"}, "absoluteBoundingBox": {"x": -35, "y": -5721, "width": 67, "height": 16}, "absoluteRenderBounds": {"x": -33.750797271728516, "y": -5716.826171875, "width": 65.16665649414062, "height": 10.9921875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Page 1 of 50", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Bold", "fontStyle": "Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 12, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "I24177:59169;908:21454", "name": "ic-right", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "rotation": -3.1415925661670117, "componentId": "1:110", "overrides": [], "uniformScaleFactor": 1.5, "children": [{"id": "I24177:59169;908:21454;764:5", "name": "Vector 36 (<PERSON><PERSON>)", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": -3.1415925661670094, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1.5, "strokeAlign": "CENTER", "strokeJoin": "ROUND", "strokeCap": "ROUND", "styles": {"fill": "1:182"}, "absoluteBoundingBox": {"x": 48.99999790185336, "y": -5719, "width": 6.000002694193029, "height": 12.000000333817297}, "absoluteRenderBounds": {"x": 48.999996185302734, "y": -5719, "width": 6.000003814697266, "height": 12}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 39.999997615814436, "y": -5725.000004482333, "width": 24.00000448233253, "height": 24.00000448233277}, "absoluteRenderBounds": {"x": 39.999996185302734, "y": -5725.000004482333, "width": 24.00000591284423, "height": 24.00000448233277}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "itemSpacing": 8, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -67, "y": -5725, "width": 131, "height": 24.000003814697266}, "absoluteRenderBounds": {"x": -67, "y": -5725, "width": 131, "height": 24.000003814697266}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "24177:59170", "name": "Frame 117404", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59171", "name": "Total results", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "1:108"}, "absoluteBoundingBox": {"x": 64, "y": -5721, "width": 64, "height": 16}, "absoluteRenderBounds": {"x": 64.59210968017578, "y": -5716.837890625, "width": 62.532371520996094, "height": 8.93408203125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Total results", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 12, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59172", "name": "Frame 117402", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59173", "name": "232", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "1:109"}, "absoluteBoundingBox": {"x": 136, "y": -5721, "width": 21, "height": 16}, "absoluteRenderBounds": {"x": 136.51458740234375, "y": -5716.77197265625, "width": 19.873886108398438, "height": 8.8681640625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "232", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Bold", "fontStyle": "Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 12, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 136, "y": -5721, "width": 21, "height": 16}, "absoluteRenderBounds": {"x": 136, "y": -5721, "width": 21, "height": 16}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "itemSpacing": 8, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 64, "y": -5721, "width": 93, "height": 16}, "absoluteRenderBounds": {"x": 64, "y": -5721, "width": 93, "height": 16}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "MAX", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -182, "y": -5725, "width": 339, "height": 24.000003814697266}, "absoluteRenderBounds": {"x": -182, "y": -5725, "width": 339, "height": 24.000003814697266}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "SPACE_BETWEEN", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -1027, "y": -5725, "width": 1184, "height": 24}, "absoluteRenderBounds": {"x": -1027, "y": -5725, "width": 1184, "height": 24}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "24177:59174", "name": "Frame 117487", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59175", "name": "Frame 1064", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24177:59176", "name": "Data from", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": -1027, "y": -5689, "width": 56, "height": 15}, "absoluteRenderBounds": {"x": -1026.0849609375, "y": -5685.27001953125, "width": 42.77325439453125, "height": 7.35009765625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Data from", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59177", "name": "Member", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": -923, "y": -5689, "width": 129, "height": 15}, "absoluteRenderBounds": {"x": -922.0850219726562, "y": -5685.365234375, "width": 36.1656494140625, "height": 7.43505859375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Member", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59178", "name": "Cost rate", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": -746, "y": -5689, "width": 60, "height": 15}, "absoluteRenderBounds": {"x": -745.5250244140625, "y": -5685.2451171875, "width": 39.1661376953125, "height": 7.3251953125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Cost rate", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59179", "name": "Contract renewal", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": -638, "y": -5689, "width": 153, "height": 15}, "absoluteRenderBounds": {"x": -637.5250244140625, "y": -5685.365234375, "width": 74.2457275390625, "height": 7.4453125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Contract renewal", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59180", "name": "Current assignment", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": -437, "y": -5689, "width": 132, "height": 15}, "absoluteRenderBounds": {"x": -436.5249938964844, "y": -5685.294921875, "width": 85.011962890625, "height": 9.1201171875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Current assignment", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59181", "name": "CV Freshness", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": -257, "y": -5689, "width": 83, "height": 15}, "absoluteRenderBounds": {"x": -256.5249938964844, "y": -5685.365234375, "width": 58.142730712890625, "height": 7.4453125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "CV Freshness", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59182", "name": "Type", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": -126, "y": -5689, "width": 117, "height": 15}, "absoluteRenderBounds": {"x": -125.8499984741211, "y": -5685.1650390625, "width": 20.54578399658203, "height": 8.8798828125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Type", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}, {"id": "24177:59183", "name": "CVs created", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4000000059604645, "g": 0.4000000059604645, "b": 0.4000000059604645, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "524:2010"}, "absoluteBoundingBox": {"x": 39, "y": -5689, "width": 53, "height": 15}, "absoluteRenderBounds": {"x": 39.474998474121094, "y": -5685.365234375, "width": 51.295005798339844, "height": 7.4453125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "CVs created", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 10, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 15, "lineHeightPercent": 124.99999237060547, "lineHeightPercentFontSize": 150, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "HORIZONTAL", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "MAX", "itemSpacing": 48, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -1027, "y": -5689, "width": 1168, "height": 15}, "absoluteRenderBounds": {"x": -1027, "y": -5689, "width": 1168, "height": 15}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "24177:59184", "name": "Line 302", "type": "LINE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8784313797950745, "g": 0.8784313797950745, "b": 0.8784313797950745, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "styles": {"stroke": "1:106"}, "absoluteBoundingBox": {"x": -1027, "y": -5670, "width": 1168, "height": 0}, "absoluteRenderBounds": {"x": -1027, "y": -5671, "width": 1168, "height": 1}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "VERTICAL", "itemSpacing": 4, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -1027, "y": -5689, "width": 1168, "height": 19}, "absoluteRenderBounds": {"x": -1027, "y": -5689, "width": 1168, "height": 19}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "24182:60024", "name": "Frame 117423", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:59989", "name": "Group 117422", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:59990", "name": "Ellipse 811", "type": "ELLIPSE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.8125, "b": 0.8125, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokeWeight": 0.7780121564865112, "strokeAlign": "INSIDE", "styles": {"fill": "1:225", "stroke": "1:104"}, "absoluteBoundingBox": {"x": -531.2366943359375, "y": -5578.44384765625, "width": 60.68494415283203, "height": 60.6849479675293}, "absoluteRenderBounds": {"x": -531.2366943359375, "y": -5578.44384765625, "width": 60.6849365234375, "height": 60.68505859375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "arcData": {"startingAngle": 0, "endingAngle": 6.2831854820251465, "innerRadius": 0}, "interactions": []}, {"id": "24182:59993", "name": "Group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:59994", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 127.21945190429688, "height": 161.81419372558594}, "absoluteRenderBounds": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 127.21945190429688, "height": 161.81396484375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:59995", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8117647171020508, "g": 0.8980392217636108, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -458.4298095703125, "y": -5468.8291015625, "width": 89.85769653320312, "height": 45.046241760253906}, "absoluteRenderBounds": {"x": -458.4298095703125, "y": -5468.8291015625, "width": 89.85769653320312, "height": 45.04638671875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:59996", "name": "Group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:59997", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 127.21945190429688, "height": 161.81419372558594}, "absoluteRenderBounds": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 127.21945190429688, "height": 161.81396484375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:59998", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -405.1177978515625, "y": -5522.01171875, "width": 11.411051750183105, "height": 11.581689834594727}, "absoluteRenderBounds": {"x": -405.1177978515625, "y": -5522.01171875, "width": 11.411041259765625, "height": 11.58154296875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 127.21945190429688, "height": 161.81419372558594}, "absoluteRenderBounds": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 127.21945190429688, "height": 161.81419372558594}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:59999", "name": "Group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:60000", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -392.1200866699219, "y": -5511.43017578125, "width": 53.356807708740234, "height": 21.463924407958984}, "absoluteRenderBounds": {"x": -392.1200866699219, "y": -5511.43017578125, "width": 53.3568115234375, "height": 21.4638671875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60001", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -437.8795166015625, "y": -5555.92236328125, "width": 29.02962875366211, "height": 38.640289306640625}, "absoluteRenderBounds": {"x": -437.8795166015625, "y": -5555.92236328125, "width": 29.029632568359375, "height": 38.64013671875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60002", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -389.9569091796875, "y": -5505.66552734375, "width": 35.889366149902344, "height": 28.257110595703125}, "absoluteRenderBounds": {"x": -389.9569091796875, "y": -5505.66552734375, "width": 35.889373779296875, "height": 28.25732421875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60003", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -445.74346923828125, "y": -5542.15576171875, "width": 31.312429428100586, "height": 24.108978271484375}, "absoluteRenderBounds": {"x": -445.74346923828125, "y": -5542.15576171875, "width": 31.31243896484375, "height": 24.10888671875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -445.7434387207031, "y": -5555.92236328125, "width": 106.9802017211914, "height": 78.51399993896484}, "absoluteRenderBounds": {"x": -445.74346923828125, "y": -5555.92236328125, "width": 106.98023223876953, "height": 78.51416015625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.1448150873184204, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 140.10455322265625, "height": 161.81419372558594}, "absoluteRenderBounds": {"x": -478.8677978515625, "y": -5567.86083984375, "width": 140.10455322265625, "height": 161.81419372558594}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "exportSettings": [{"suffix": "", "format": "PNG", "constraint": {"type": "SCALE", "value": 1}}], "effects": [], "interactions": []}, {"id": "24182:60027", "name": "Group 117423", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:59992", "name": "Ellipse 813", "type": "ELLIPSE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0.9176470637321472, "b": 0.6941176652908325, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokeWeight": 0.5404662489891052, "strokeAlign": "INSIDE", "styles": {"fill": "1:226", "stroke": "1:104"}, "absoluteBoundingBox": {"x": -506.078125, "y": -5634, "width": 42.15636444091797, "height": 42.156368255615234}, "absoluteRenderBounds": {"x": -506.078125, "y": -5634, "width": 42.1563720703125, "height": 42.15625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "arcData": {"startingAngle": 0, "endingAngle": 6.2831854820251465, "innerRadius": 0}, "interactions": []}, {"id": "24182:60004", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 0.15203410387039185, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -496.3497314453125, "y": -5629.5927734375, "width": 27.563777923583984, "height": 37.847896575927734}, "absoluteRenderBounds": {"x": -496.3497314453125, "y": -5629.5927734375, "width": 27.56378173828125, "height": 37.84765625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60005", "name": "Group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:60006", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.15203410387039185, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -496.0605773925781, "y": -5629.67626953125, "width": 26.621164321899414, "height": 37.885658264160156}, "absoluteRenderBounds": {"x": -496.0605773925781, "y": -5629.67626953125, "width": 26.62115478515625, "height": 37.8857421875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60007", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.15203410387039185, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -475.64935302734375, "y": -5600.654296875, "width": 1.83116614818573, "height": 8.909192085266113}, "absoluteRenderBounds": {"x": -475.64935302734375, "y": -5600.654296875, "width": 1.8311767578125, "height": 8.9091796875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60008", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.15203410387039185, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -491.271484375, "y": -5598.1455078125, "width": 2.902560234069824, "height": 6.415839195251465}, "absoluteRenderBounds": {"x": -491.271484375, "y": -5598.1455078125, "width": 2.902557373046875, "height": 6.416015625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60009", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.15203410387039185, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -479.98590087890625, "y": -5621.46630859375, "width": 2.8570926189422607, "height": 4.711395740509033}, "absoluteRenderBounds": {"x": -479.98590087890625, "y": -5621.46630859375, "width": 2.857086181640625, "height": 4.71142578125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.15203410387039185, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -496.060546875, "y": -5629.67626953125, "width": 26.621164321899414, "height": 37.94657897949219}, "absoluteRenderBounds": {"x": -496.0605773925781, "y": -5629.67626953125, "width": 26.62119483947754, "height": 37.94677734375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -506.078125, "y": -5634, "width": 42.15636444091797, "height": 42.27030944824219}, "absoluteRenderBounds": {"x": -506.078125, "y": -5634, "width": 42.1563720703125, "height": 42.2705078125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "24182:60010", "name": "Group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:60011", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fillOverrideTable": {"1": null}, "strokes": [], "strokeWeight": 0.23186086118221283, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -519.47705078125, "y": -5575.60595703125, "width": 37.87731170654297, "height": 60.03457260131836}, "absoluteRenderBounds": {"x": -519.47705078125, "y": -5575.60595703125, "width": 37.8773193359375, "height": 60.03466796875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60012", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.23186086118221283, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -524.234619140625, "y": -5580, "width": 42.80196762084961, "height": 64.26075744628906}, "absoluteRenderBounds": {"x": -524.234619140625, "y": -5580, "width": 42.801971435546875, "height": 64.2607421875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60013", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.23186086118221283, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -512.325439453125, "y": -5527.84716796875, "width": 3.768082618713379, "height": 11.945639610290527}, "absoluteRenderBounds": {"x": -512.325439453125, "y": -5527.84716796875, "width": 3.768096923828125, "height": 11.94580078125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60014", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.23186086118221283, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -490.22894287109375, "y": -5526.40478515625, "width": 2.133122682571411, "height": 10.364176750183105}, "absoluteRenderBounds": {"x": -490.22894287109375, "y": -5526.40478515625, "width": 2.13311767578125, "height": 10.3642578125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60015", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.23186086118221283, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -497.57965087890625, "y": -5554.41552734375, "width": 2.5765514373779297, "height": 3.0854926109313965}, "absoluteRenderBounds": {"x": -497.57965087890625, "y": -5554.41552734375, "width": 2.5765380859375, "height": 3.08544921875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60016", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0.23186086118221283, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -491.18896484375, "y": -5554.14697265625, "width": 2.9388391971588135, "height": 3.1422438621520996}, "absoluteRenderBounds": {"x": -491.18896484375, "y": -5554.14697265625, "width": 2.9388427734375, "height": 3.14208984375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.23186086118221283, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -524.234619140625, "y": -5580, "width": 42.80196762084961, "height": 64.42874908447266}, "absoluteRenderBounds": {"x": -524.234619140625, "y": -5580, "width": 42.801971435546875, "height": 64.42874908447266}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60028", "name": "Group 117424", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:59991", "name": "Ellipse 812", "type": "ELLIPSE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8117647171020508, "g": 0.8980392217636108, "b": 1, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokeWeight": 0.532878041267395, "strokeAlign": "INSIDE", "styles": {"fill": "1:228", "stroke": "1:104"}, "absoluteBoundingBox": {"x": -449.2366943359375, "y": -5626, "width": 41.564483642578125, "height": 41.56448745727539}, "absoluteRenderBounds": {"x": -449.2366943359375, "y": -5626, "width": 41.564483642578125, "height": 41.564453125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "arcData": {"startingAngle": 0, "endingAngle": 6.2831854820251465, "innerRadius": 0}, "interactions": []}, {"id": "24182:60017", "name": "Group 117421", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:60018", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 0.16143374145030975, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -442.23614501953125, "y": -5618.4921875, "width": 30.833837509155273, "height": 35.014976501464844}, "absoluteRenderBounds": {"x": -442.23614501953125, "y": -5618.4921875, "width": 30.833831787109375, "height": 35.01513671875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60019", "name": "Group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "24182:60020", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.13725490868091583, "g": 0.12156862765550613, "b": 0.125490203499794, "a": 1}}], "strokes": [], "strokeWeight": 0.16143374145030975, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -442.30926513671875, "y": -5618.53955078125, "width": 30.888580322265625, "height": 35.079551696777344}, "absoluteRenderBounds": {"x": -442.30926513671875, "y": -5618.53955078125, "width": 30.888580322265625, "height": 35.07958984375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60021", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.13725490868091583, "g": 0.12156862765550613, "b": 0.125490203499794, "a": 1}}], "strokes": [], "strokeWeight": 0.16143374145030975, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -435.343017578125, "y": -5607.31982421875, "width": 4.706550598144531, "height": 5.020585060119629}, "absoluteRenderBounds": {"x": -435.343017578125, "y": -5607.31982421875, "width": 4.70654296875, "height": 5.0205078125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "24182:60022", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.13725490868091583, "g": 0.12156862765550613, "b": 0.125490203499794, "a": 1}}], "strokes": [], "strokeWeight": 0.16143374145030975, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -424.1706848144531, "y": -5616.06591796875, "width": 4.679991722106934, "height": 6.227303504943848}, "absoluteRenderBounds": {"x": -424.1706848144531, "y": -5616.06591796875, "width": 4.67999267578125, "height": 6.2275390625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.16143374145030975, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -442.30926513671875, "y": -5618.53955078125, "width": 30.888580322265625, "height": 35.079551696777344}, "absoluteRenderBounds": {"x": -442.30926513671875, "y": -5618.53955078125, "width": 30.888580322265625, "height": 35.07958984375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.532878041267395, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -442.30926513671875, "y": -5618.53955078125, "width": 30.906949996948242, "height": 35.079551696777344}, "absoluteRenderBounds": {"x": -442.30926513671875, "y": -5618.53955078125, "width": 30.906951904296875, "height": 35.07958984375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -449.2366943359375, "y": -5626, "width": 41.564483642578125, "height": 42.540000915527344}, "absoluteRenderBounds": {"x": -449.2366943359375, "y": -5626, "width": 41.564483642578125, "height": 42.5400390625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 0.7780121564865112, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": -531.2366943359375, "y": -5634, "width": 192.47344970703125, "height": 227.95335388183594}, "absoluteRenderBounds": {"x": -531.2366943359375, "y": -5634, "width": 192.47344970703125, "height": 227.95335388183594}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "24182:60025", "name": "No profiles found.", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:104", "text": "1:265"}, "absoluteBoundingBox": {"x": -490.5, "y": -5398.046875, "width": 111, "height": 20}, "absoluteRenderBounds": {"x": -489.218994140625, "y": -5393.35791015625, "width": 108.11904907226562, "height": 12.7119140625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "No profiles found.", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 19.600000381469727, "lineHeightPercent": 116.66666412353516, "lineHeightPercentFontSize": 140, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingTop": 24, "paddingBottom": 24, "itemSpacing": 8, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -1027, "y": -5658, "width": 1184, "height": 303.953369140625}, "absoluteRenderBounds": {"x": -1027, "y": -5658, "width": 1184, "height": 303.953369140625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 12, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -1027, "y": -5725, "width": 1184, "height": 370.953369140625}, "absoluteRenderBounds": {"x": -1027, "y": -5725, "width": 1184, "height": 370.953369140625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, "components": {"620:4151": {"key": "d5068be467c413308d2aa8d654eba8a79998779a", "name": "type=text_ic", "description": "", "remote": true, "componentSetId": "620:4150", "documentationLinks": []}, "1:180": {"key": "504a19794698850e2a9b2a84c65ebfdab28f6650", "name": "ic-dropdown-small", "description": "", "remote": true, "documentationLinks": []}, "908:21480": {"key": "9abc2435455cbfa6037ebb94cd80563d1f0a6595", "name": "type=start", "description": "", "remote": false, "componentSetId": "908:21484", "documentationLinks": []}, "1:110": {"key": "5b87f6ee657420c7a84e4a954f8f57b7a1fd8a39", "name": "ic-back-16", "description": "", "remote": true, "documentationLinks": []}}, "componentSets": {"620:4150": {"key": "e6dcff8a88035687d09ae54d5f127e25145126e1", "name": "dropdown", "description": "", "remote": true}, "908:21484": {"key": "1b90990c2cca7731e5d22b1bafcec59f3d31074a", "name": "pages", "description": "", "remote": false}}, "schemaVersion": 0, "styles": {"1:104": {"key": "b5779d4987d1a961cb0d1a2ca1cb3e427769a3bd", "name": "msBlack", "styleType": "FILL", "remote": true, "description": ""}, "1:109": {"key": "68b622ad137feb1a411e7be7e02a965d97408839", "name": "Small Doge/smalldoge-4B", "styleType": "TEXT", "remote": true, "description": ""}, "1:103": {"key": "cc1bef04de6e05ff5969ad995789c31b299e7dc6", "name": "msW<PERSON>e", "styleType": "FILL", "remote": true, "description": ""}, "1:106": {"key": "106eeac967531f1cf3f1d03beaf21cfa5cecbcf9", "name": "msGray-5", "styleType": "FILL", "remote": true, "description": ""}, "1:182": {"key": "637b23cdeeacc4b559915e52c173ace9f64cf358", "name": "msGray-3", "styleType": "FILL", "remote": true, "description": ""}, "1:108": {"key": "dea7c4e1f1f1a63c1581d0da078a8b1b45ecd414", "name": "Small Doge/smalldoge-4R", "styleType": "TEXT", "remote": true, "description": ""}, "524:2010": {"key": "8866aaa2def93cfeb4d935d9f27f3700acaf5547", "name": "Small Doge/smalldoge-5R", "styleType": "TEXT", "remote": true, "description": ""}, "1:225": {"key": "1a9300f85032bf391778ed91539a6356c93de5fb", "name": "msRed-3", "styleType": "FILL", "remote": true, "description": ""}, "1:226": {"key": "5713d0a3099ae5bf5f98a07a50d27d096e72fecf", "name": "msGreen-3", "styleType": "FILL", "remote": true, "description": ""}, "1:228": {"key": "ac06d4eb2b5f6801f78f45c969e107f7089a1612", "name": "msBlue-3", "styleType": "FILL", "remote": true, "description": ""}, "1:265": {"key": "d73931f7db52940f69d065e6078361de526b3bdc", "name": "Small Doge/smalldoge-3R", "styleType": "TEXT", "remote": true, "description": ""}}}}}