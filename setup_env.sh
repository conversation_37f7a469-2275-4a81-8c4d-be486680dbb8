#!/bin/bash
# Generated on $(date -u +%Y-%m-%d)

rm -f .env

echo "# Generated on $(date -u +%Y-%m-%d)" >> .env
echo "" >> .env

echo "VITE_API_BASE_URL=${VITE_API_BASE_URL}" >> .env
echo "VITE_WEB_BASE_URL=${VITE_WEB_BASE_URL}" >> .env

echo "NODE_ENV=${NODE_ENV:-production}" >> .env
echo "PORT=${PORT:-3000}" >> .env
echo "JWT_SECRET_KEY=${JWT_SECRET_KEY}" >> .env
echo "BASIC_AUTH_PASSWORD=${BASIC_AUTH_PASSWORD}" >>.env
echo "MONGO_URI=${MONGO_URI}" >> .env

echo "SENDGRID_API_KEY=${SENDGRID_API_KEY}" >> .env
echo "SENDGRID_FROM_EMAIL=${SENDGRID_FROM_EMAIL}" >> .env

echo "AWS_REGION=${AWS_REGION:-eu-north-1}" >> .env
echo "AWS_BUCKET=${AWS_BUCKET}" >> .env
echo "AWS_BUCKET_URL=${AWS_BUCKET_URL}" >> .env
echo "AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}" >> .env
echo "AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}" >> .env

echo "STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}" >> .env
echo "STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}" >> .env

echo "DOCKER_REGISTRY_USER=${DOCKER_REGISTRY_USER}" >>.env
echo "DOCKER_REGISTRY_TOKEN=${DOCKER_REGISTRY_TOKEN}" >>.env
echo "DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}" >>.env

echo "CI_REGISTRY=${CI_REGISTRY}" >>.env
echo "CI_PROJECT_NAME=${CI_PROJECT_NAME}" >>.env
echo "CI_COMMIT_REF_SLUG=${CI_COMMIT_REF_SLUG}" >>.env
echo "CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME}" >>.env
CI_COMMIT_MESSAGE=$(echo "$CI_COMMIT_MESSAGE" | base64 -w 0)
echo "CI_COMMIT_MESSAGE=${CI_COMMIT_MESSAGE}" >>.env
echo "CI_COMMIT_SHA=${CI_COMMIT_SHA}" >>.env

echo "SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}" >>.env

echo "CV_INVENTORY_TOKEN=${CV_INVENTORY_TOKEN}" >>.env
echo "AI_API_URL=${AI_API_URL}" >> .env
echo "AI_API_TOKEN=${AI_API_TOKEN}" >> .env
